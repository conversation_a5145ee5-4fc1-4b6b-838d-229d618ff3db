<template>
  <div class="check-record-config">
    <div class="config-container">
      <!-- 左侧：数据查询区域 -->
      <div class="left-panel">
        <div class="search-section">
          <h3>检验记录数据查询</h3>
          <div class="search-form">
            <div class="form-item">
              <label>车辆ID:</label>
              <input
                v-model="searchForm.carId"
                type="text"
                placeholder="请输入车辆ID"
                class="search-input"
                @keyup.enter="handleSearch"
              >
            </div>
            <div class="form-actions">
              <button @click="handleSearch" class="search-btn">查询</button>
              <button @click="handleReset" class="reset-btn">重置</button>
            </div>
          </div>
        </div>

        <!-- 数据列表 -->
        <div class="data-section">
          <div class="data-header">
            <h4>检验记录列表</h4>
            <div class="pagination-info">
              共 {{ pagination.total }} 条记录
            </div>
          </div>

          <div class="data-table">
            <table>
              <thead>
                <tr>
                  <th>检查工序名称</th>
                  <th>检查项目及技术条件</th>
                  <th>实际检查结果</th>
                  <th>完工月</th>
                  <th>完工日</th>
                  <th>操作员</th>
                  <th>班组长</th>
                  <th>检验员</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(record, index) in recordList" :key="index">
                  <td>{{ record.check_name }}</td>
                  <td>{{ record.check_content }}</td>
                  <td>{{ record.result }}</td>
                  <td>{{ record.month_str }}</td>
                  <td>{{ record.day_str }}</td>
                  <td>{{ record.check_user_name }}</td>
                  <td>{{ record.bzz }}</td>
                  <td>{{ record.jyy }}</td>
                  <td>
                    <button @click="insertRecord(record)" class="insert-btn">插入</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 分页 -->
          <div class="pagination">
            <button
              @click="changePage(pagination.current - 1)"
              :disabled="pagination.current <= 1"
              class="page-btn"
            >
              上一页
            </button>
            <span class="page-info">
              第 {{ pagination.current }} 页 / 共 {{ pagination.pages }} 页
            </span>
            <button
              @click="changePage(pagination.current + 1)"
              :disabled="pagination.current >= pagination.pages"
              class="page-btn"
            >
              下一页
            </button>
          </div>
        </div>
      </div>

      <!-- 右侧：表格渲染区域 -->
      <div class="right-panel">
        <div class="table-section">
          <div class="table-header">
            <h3>检验记录表</h3>
            <div class="table-actions">
              <button @click="saveTableData" class="save-btn">保存表格</button>
              <button @click="clearTableData" class="clear-btn">清空表格</button>
            </div>
          </div>

          <!-- 表格容器 -->
          <div class="table-container">
            <TableContainer
              ref="tableContainer"
              :table-width="'100%'"
              :table-height="'600px'"
              :data-rows="tableData"
              @data-inserted="handleDataInserted"
              @table-updated="handleTableUpdated"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 保存结果提示 -->
    <div v-if="saveResult" class="save-result" :class="saveResult.success ? 'success' : 'error'">
      {{ saveResult.message }}
    </div>
  </div>
</template>

<script>
import TableContainer from '@/components/TableContainer.vue'
import { getCheckRecord } from '@/api/word/checkRecord'
import { saveOrUpdateDesignWord, getDesignWordByCarId } from '@/api/word/designWord'

export default {
  name: 'CheckRecordConfig',
  components: {
    TableContainer
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        carId: '0822'
      },

      // 记录列表
      recordList: [],

      // 分页信息
      pagination: {
        current: 1,
        size: 10,
        total: 0,
        pages: 0
      },

      // 表格数据
      tableData: [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ],

      // 表格配置
      tableConfig: null,

      // 保存结果
      saveResult: null,

      // 当前车辆的设计ID
      currentDesignId: null
    }
  },
  mounted() {
    this.initializeTable()
    this.handleSearch()
  },
  methods: {
    /**
     * 初始化表格配置
     */
    initializeTable() {
      // 设置默认表格配置
      this.tableConfig = {
        title: "检验记录表",
        headers: [
          [
            "检查工序\n名称",
            "检 查 项 目 及 技 术 条 件",
            "实 际 检 查 结 果",
            "完工",
            "",
            "操作员",
            "班组长",
            "检验员"
          ],
          [
            "",
            "",
            "",
            "月",
            "日",
            "",
            "",
            ""
          ]
        ],
        headerMerges: [
          {
            startRow: 0,
            startCol: 0,
            endRow: 1,
            endCol: 0,
            content: "检查工序\n名称"
          },
          {
            startRow: 0,
            startCol: 1,
            endRow: 1,
            endCol: 1,
            content: "检 查 项 目 及 技 术 条 件"
          },
          {
            startRow: 0,
            startCol: 2,
            endRow: 1,
            endCol: 2,
            content: "实 际 检 查 结 果"
          },
          {
            startRow: 0,
            startCol: 3,
            endRow: 0,
            endCol: 4,
            content: "完工"
          },
          {
            startRow: 0,
            startCol: 5,
            endRow: 1,
            endCol: 5,
            content: "操作员"
          },
          {
            startRow: 0,
            startCol: 6,
            endRow: 1,
            endCol: 6,
            content: "班组长"
          },
          {
            startRow: 0,
            startCol: 7,
            endRow: 1,
            endCol: 7,
            content: "检验员"
          }
        ],
        headerWidthConfig: {
          columnWidths: [100, 460, 160, 32, 32, 32, 32, 32],
          headerHeights: [35, 35]
        },
        verticalHeadersConfig: [false, false, false, false, false, true, true, true]
      }

      // 应用表格配置
      this.$nextTick(() => {
        this.applyTableConfig()
      })
    },

    /**
     * 应用表格配置
     */
    applyTableConfig() {
      const tableContainer = this.$refs.tableContainer
      if (tableContainer && this.tableConfig) {
        tableContainer.setDynamicHeaderConfig(
          true,
          {
            headers: this.tableConfig.headers,
            merges: this.tableConfig.headerMerges
          },
          this.tableConfig.headerWidthConfig,
          this.tableConfig.verticalHeadersConfig
        )
      }
    },

    /**
     * 搜索记录
     */
    async handleSearch() {
      try {
        const params = {
          pageNum: this.pagination.current,
          pageSize: this.pagination.size,
          carId: this.searchForm.carId
        }

        const response = await getCheckRecord(params)
        if (response.code === 200) {
          const data = response.data
          this.recordList = data.records || []
          this.pagination.total = data.total || 0
          this.pagination.pages = data.pages || 0
          this.pagination.current = data.current || 1
        }

        // 加载对应车辆的表格设计
        if (this.searchForm.carId) {
          await this.loadTableDesign(this.searchForm.carId)
        }

      } catch (error) {
        console.error('查询检验记录失败:', error)
        this.showSaveResult(false, '查询检验记录失败')
      }
    },

    /**
     * 重置搜索
     */
    handleReset() {
      this.searchForm.carId = ''
      this.pagination.current = 1
      this.recordList = []
      this.pagination.total = 0
      this.pagination.pages = 0
    },

    /**
     * 切换页码
     */
    changePage(page) {
      if (page >= 1 && page <= this.pagination.pages) {
        this.pagination.current = page
        this.handleSearch()
      }
    },

    /**
     * 插入记录到表格
     */
    insertRecord(record) {
      try {
        const tableContainer = this.$refs.tableContainer
        if (!tableContainer) {
          this.showSaveResult(false, '表格组件未找到')
          return
        }

        // 构建插入数据
        const insertData = {
          cellRows: [[
            { content: record.check_name, originContent: record.check_name, hasMath: false },
            { content: record.check_content, originContent: record.check_content, hasMath: false },
            { content: record.result, originContent: record.result, hasMath: false },
            { content: record.month_str.toString(), originContent: record.month_str.toString(), hasMath: false },
            { content: record.day_str.toString(), originContent: record.day_str.toString(), hasMath: false },
            { content: record.check_user_name, originContent: record.check_user_name, hasMath: false },
            { content: record.bzz, originContent: record.bzz, hasMath: false },
            { content: record.jyy, originContent: record.jyy, hasMath: false }
          ]]
        }

        const options = {
          clearExisting: false,
          validateData: true,
          startRow: this.tableData.length
        }

        const result = tableContainer.insertDataFromJSON(insertData, options)
        if (result.success) {
          this.tableData = tableContainer.dataRows
          this.showSaveResult(true, '数据插入成功')
        } else {
          this.showSaveResult(false, result.message || '数据插入失败')
        }

      } catch (error) {
        console.error('插入记录失败:', error)
        this.showSaveResult(false, '插入记录失败')
      }
    },

    /**
     * 加载表格设计
     */
    async loadTableDesign(carId) {
      try {
        const response = await getDesignWordByCarId(carId)
        if (response.code === 200 && response.data) {
          const designData = response.data
          this.currentDesignId = designData.id

          // 解析表格配置
          if (designData.tableConfig) {
            this.tableConfig = JSON.parse(designData.tableConfig)
            this.applyTableConfig()
          }

          // 解析表格数据
          if (designData.tableData) {
            const tableData = JSON.parse(designData.tableData)
            if (tableData && Array.isArray(tableData)) {
              this.tableData = tableData
              const tableContainer = this.$refs.tableContainer
              if (tableContainer) {
                tableContainer.dataRows = tableData
              }
            }
          }
        }
      } catch (error) {
        console.error('加载表格设计失败:', error)
      }
    },

    /**
     * 保存表格数据
     */
    async saveTableData() {
      try {
        const tableContainer = this.$refs.tableContainer
        if (!tableContainer) {
          this.showSaveResult(false, '表格组件未找到')
          return
        }

        // 获取表格数据
        const tableData = tableContainer.dataRows

        // 构建保存数据
        const saveData = {
          id: this.currentDesignId,
          carId: this.searchForm.carId,
          title: this.tableConfig.title || '检验记录表',
          tableConfig: JSON.stringify(this.tableConfig),
          tableData: JSON.stringify(tableData),
          status: 1
        }

        const response = await saveOrUpdateDesignWord(saveData)
        if (response.code === 200) {
          this.showSaveResult(true, '表格保存成功')
          // 更新当前设计ID
          if (!this.currentDesignId && response.data) {
            this.currentDesignId = response.data
          }
        } else {
          this.showSaveResult(false, response.msg || '表格保存失败')
        }

      } catch (error) {
        console.error('保存表格数据失败:', error)
        this.showSaveResult(false, '保存表格数据失败')
      }
    },

    /**
     * 清空表格数据
     */
    clearTableData() {
      this.tableData = [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ]

      const tableContainer = this.$refs.tableContainer
      if (tableContainer) {
        tableContainer.dataRows = this.tableData
      }

      this.showSaveResult(true, '表格已清空')
    },

    /**
     * 显示保存结果
     */
    showSaveResult(success, message) {
      this.saveResult = { success, message }
      setTimeout(() => {
        this.saveResult = null
      }, 3000)
    },

    /**
     * 处理数据插入事件
     */
    handleDataInserted(event) {
      console.log('数据插入事件:', event)
    },

    /**
     * 处理表格更新事件
     */
    handleTableUpdated() {
      console.log('表格更新事件')
    }
  }
}
</script>

<style scoped>
.check-record-config {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.config-container {
  display: flex;
  gap: 20px;
  height: calc(100vh - 40px);
}

/* 左侧面板 */
.left-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.search-section {
  margin-bottom: 20px;
}

.search-section h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-item label {
  min-width: 80px;
  font-weight: 500;
  color: #333;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-actions {
  display: flex;
  gap: 10px;
}

.search-btn, .reset-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-btn {
  background: #007bff;
  color: white;
}

.search-btn:hover {
  background: #0056b3;
}

.reset-btn {
  background: #6c757d;
  color: white;
}

.reset-btn:hover {
  background: #545b62;
}

/* 数据区域 */
.data-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.data-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.data-table {
  flex: 1;
  overflow: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th,
.data-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 500;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 1;
}

.data-table tr:hover {
  background: #f8f9fa;
}

.insert-btn {
  padding: 4px 8px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s ease;
}

.insert-btn:hover {
  background: #218838;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 15px;
  padding: 15px 0;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.save-btn, .clear-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover {
  background: #218838;
}

.clear-btn {
  background: #dc3545;
  color: white;
}

.clear-btn:hover {
  background: #c82333;
}

.table-container {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

/* 保存结果提示 */
.save-result {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

.save-result.success {
  background: #28a745;
}

.save-result.error {
  background: #dc3545;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .config-container {
    flex-direction: column;
    height: auto;
  }

  .left-panel, .right-panel {
    flex: none;
    height: auto;
  }

  .data-table {
    max-height: 400px;
  }

  .table-container {
    height: 500px;
  }
}
</style>
