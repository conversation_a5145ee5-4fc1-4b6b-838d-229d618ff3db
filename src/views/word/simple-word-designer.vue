<template>
  <div class="word-designer">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-row :gutter="10">
        <!-- 文件操作 -->
        <el-col :span="4">
          <el-button-group>
            <el-button size="small" icon="el-icon-document" @click="newDocument">新建</el-button>
            <el-button size="small" icon="el-icon-download" @click="exportDocument">导出</el-button>
          </el-button-group>
        </el-col>

        <!-- 字体设置 -->
        <el-col :span="8">
          <el-row :gutter="5">
            <el-col :span="8">
              <el-select
                v-model="fontFamily"
                size="small"
                placeholder="字体"
                @focus="saveSelection"
                @visible-change="onFontFamilySelectVisibleChange"
                @change="applyFontFamily">
                <el-option label="宋体" value="SimSun"></el-option>
                <el-option label="黑体" value="SimHei"></el-option>
                <el-option label="微软雅黑" value="Microsoft YaHei"></el-option>
                <el-option label="Arial" value="Arial"></el-option>
                <el-option label="Times New Roman" value="Times New Roman"></el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-input-number
                v-model="fontSizeNumber"
                size="small"
                :min="8"
                :max="72"
                :step="1"
                @focus="saveSelection"
                @change="onFontSizeNumberChange"
                placeholder="字号">
              </el-input-number>
            </el-col>
            <el-col :span="2">
              <el-select
                v-model="fontSize"
                size="small"
                @focus="saveSelection"
                @visible-change="onFontSizeSelectVisibleChange"
                @change="applyFontSize"
                style="width: 60px;">
                <el-option label="8" value="8px"></el-option>
                <el-option label="9" value="9px"></el-option>
                <el-option label="10" value="10px"></el-option>
                <el-option label="11" value="11px"></el-option>
                <el-option label="12" value="12px"></el-option>
                <el-option label="14" value="14px"></el-option>
                <el-option label="16" value="16px"></el-option>
                <el-option label="18" value="18px"></el-option>
                <el-option label="20" value="20px"></el-option>
                <el-option label="22" value="22px"></el-option>
                <el-option label="24" value="24px"></el-option>
                <el-option label="26" value="26px"></el-option>
                <el-option label="28" value="28px"></el-option>
                <el-option label="30" value="30px"></el-option>
                <el-option label="32" value="32px"></el-option>
                <el-option label="36" value="36px"></el-option>
                <el-option label="42" value="42px"></el-option>
                <el-option label="48" value="48px"></el-option>
                <el-option label="54" value="54px"></el-option>
                <el-option label="60" value="60px"></el-option>
                <el-option label="72" value="72px"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-color-picker
                v-model="fontColor"
                size="small"
                @focus="saveSelection"
                @active-change="saveSelection"
                @change="applyFontColor">
              </el-color-picker>
            </el-col>
            <el-col :span="4">
              <el-button-group>
                <el-button size="small" @mousedown="saveSelection" @click="decreaseFontSize" title="减小字体">A-</el-button>
                <el-button size="small" @mousedown="saveSelection" @click="increaseFontSize" title="增大字体">A+</el-button>
              </el-button-group>
            </el-col>
          </el-row>
        </el-col>

        <!-- 格式化按钮 -->
        <el-col :span="8">
          <el-button-group>
            <el-button size="small" :class="{'is-active': isBold}" @mousedown="saveSelection" @click="toggleBold">
              <strong>B</strong>
            </el-button>
            <el-button size="small" :class="{'is-active': isItalic}" @mousedown="saveSelection" @click="toggleItalic">
              <em>I</em>
            </el-button>
            <el-button size="small" :class="{'is-active': isUnderline}" @mousedown="saveSelection" @click="toggleUnderline">
              <u>U</u>
            </el-button>
            <el-button size="small" :class="{'is-active': isStrikethrough}" @mousedown="saveSelection" @click="toggleStrikethrough">
              <s>S</s>
            </el-button>
          </el-button-group>
        </el-col>

        <!-- 对齐方式 -->
        <el-col :span="4">
          <el-button-group>
            <el-button size="small" icon="el-icon-align-left" @mousedown="saveSelection" @click="setAlignment('left')"></el-button>
            <el-button size="small" icon="el-icon-align-center" @mousedown="saveSelection" @click="setAlignment('center')"></el-button>
            <el-button size="small" icon="el-icon-align-right" @mousedown="saveSelection" @click="setAlignment('right')"></el-button>
          </el-button-group>
        </el-col>
      </el-row>

      <!-- 第二行工具栏 -->
      <el-row :gutter="10" style="margin-top: 10px;">
        <!-- 缩进和间距 -->
        <el-col :span="6">
          <el-row :gutter="5">
            <el-col :span="8">
              <el-button size="small" icon="el-icon-back" @click="decreaseIndent" title="减少缩进"></el-button>
              <el-button size="small" icon="el-icon-right" @click="increaseIndent" title="增加缩进"></el-button>
            </el-col>
            <el-col :span="8">
              <el-input-number v-model="lineHeight" size="small" :min="1" :max="3" :step="0.1"
                               @change="applyLineHeight" placeholder="行间距"></el-input-number>
            </el-col>
            <el-col :span="8">
              <el-input-number v-model="paragraphSpacing" size="small" :min="0" :max="50"
                               @change="applyParagraphSpacing" placeholder="段落间距"></el-input-number>
            </el-col>
          </el-row>
        </el-col>

        <!-- 段落格式指示器 -->
        <el-col :span="2">
          <el-tag size="small" :type="currentParagraphHasCustomFont ? 'success' : 'info'">
            {{ currentParagraphHasCustomFont ? '已设置' : '默认' }}
          </el-tag>
        </el-col>

        <!-- 图片插入 -->
        <el-col :span="4">
          <el-upload
            action="#"
            :show-file-list="false"
            :before-upload="handleImageUpload"
            :auto-upload="false"
            accept="image/*">
            <el-button size="small" icon="el-icon-picture">插入图片</el-button>
          </el-upload>
        </el-col>

        <!-- 页面设置 -->
        <el-col :span="4">
          <el-button size="small" icon="el-icon-setting" @click="showPageSettings = true">页面设置</el-button>
        </el-col>

        <!-- 预览 -->
        <el-col :span="3">
          <el-button size="small" icon="el-icon-view" @click="previewDocument">预览</el-button>
        </el-col>

        <!-- 帮助 -->
        <el-col :span="1">
          <el-button size="small" icon="el-icon-question" @click="showHelp = true" title="快捷键帮助"></el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 编辑区域 -->
    <div class="editor-container">
      <!-- 标尺 -->
      <div class="ruler">
        <div class="ruler-numbers">
          <span v-for="i in 20" :key="i" :style="{left: (i * 40) + 'px'}">{{ i }}</span>
        </div>
      </div>

      <!-- 页面容器 -->
      <div class="page-container" :style="pageContainerStyle">
        <!-- 页眉 -->
        <div v-if="pageSettings.showHeader" class="page-header"
             :style="headerFooterStyle"
             contenteditable="true"
             @input="updateHeader">
          {{ pageSettings.headerText }}
        </div>

        <!-- 主要内容区域 -->
        <div
          ref="editor"
          class="editor-content"
          :style="editorStyle"
          contenteditable="true"
          @input="handleContentChange"
          @keyup="updateFormatState"
          @mouseup="updateFormatState"
          @paste="handlePaste"
          @keydown="handleKeyDown"
          @click="handleEditorClick">
          <p>在此输入您的文档内容...</p>
        </div>

        <!-- 页脚 -->
        <div v-if="pageSettings.showFooter" class="page-footer"
             :style="headerFooterStyle"
             contenteditable="true"
             @input="updateFooter">
          <span v-if="pageSettings.showPageNumber">第 {{ currentPage }} 页</span>
          {{ pageSettings.footerText }}
        </div>
      </div>
    </div>

    <!-- 页面设置对话框 -->
    <el-dialog title="页面设置" :visible.sync="showPageSettings" width="600px">
      <el-form :model="pageSettings" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="纸张大小">
              <el-select v-model="pageSettings.paperSize" @change="applyPaperSize">
                <el-option label="A4" value="A4"></el-option>
                <el-option label="A3" value="A3"></el-option>
                <el-option label="Letter" value="Letter"></el-option>
                <el-option label="自定义" value="Custom"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="页面方向">
              <el-radio-group v-model="pageSettings.orientation" @change="applyOrientation">
                <el-radio label="portrait">纵向</el-radio>
                <el-radio label="landscape">横向</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="上边距">
              <el-input-number v-model="pageSettings.marginTop" :min="0" :max="100" size="small"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="下边距">
              <el-input-number v-model="pageSettings.marginBottom" :min="0" :max="100" size="small"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="左边距">
              <el-input-number v-model="pageSettings.marginLeft" :min="0" :max="100" size="small"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="右边距">
              <el-input-number v-model="pageSettings.marginRight" :min="0" :max="100" size="small"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="显示页眉">
          <el-switch v-model="pageSettings.showHeader"></el-switch>
        </el-form-item>

        <el-form-item v-if="pageSettings.showHeader" label="页眉内容">
          <el-input v-model="pageSettings.headerText" placeholder="请输入页眉内容"></el-input>
        </el-form-item>

        <el-form-item label="显示页脚">
          <el-switch v-model="pageSettings.showFooter"></el-switch>
        </el-form-item>

        <el-form-item v-if="pageSettings.showFooter" label="页脚内容">
          <el-input v-model="pageSettings.footerText" placeholder="请输入页脚内容"></el-input>
        </el-form-item>

        <el-form-item label="显示页码">
          <el-switch v-model="pageSettings.showPageNumber"></el-switch>
        </el-form-item>

        <el-form-item label="页眉页脚字体">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-select v-model="pageSettings.headerFooterFont" size="small">
                <el-option label="宋体" value="SimSun"></el-option>
                <el-option label="黑体" value="SimHei"></el-option>
                <el-option label="微软雅黑" value="Microsoft YaHei"></el-option>
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input-number v-model="pageSettings.headerFooterFontSize" :min="8" :max="24" size="small"></el-input-number>
            </el-col>
            <el-col :span="8">
              <el-color-picker v-model="pageSettings.headerFooterColor" size="small"></el-color-picker>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showPageSettings = false">取消</el-button>
        <el-button type="primary" @click="applyPageSettings">确定</el-button>
      </div>
    </el-dialog>

    <!-- 帮助对话框 -->
    <el-dialog title="快捷键帮助" :visible.sync="showHelp" width="500px">
      <div class="help-content">
        <h4>文本格式化</h4>
        <ul>
          <li><kbd>Ctrl + B</kbd> - 加粗</li>
          <li><kbd>Ctrl + I</kbd> - 斜体</li>
          <li><kbd>Ctrl + U</kbd> - 下划线</li>
          <li><kbd>Ctrl + +</kbd> - 增大字体</li>
          <li><kbd>Ctrl + -</kbd> - 减小字体</li>
        </ul>

        <h4>文档操作</h4>
        <ul>
          <li><kbd>Ctrl + S</kbd> - 导出文档</li>
          <li><kbd>Ctrl + P</kbd> - 预览文档</li>
          <li><kbd>Ctrl + Z</kbd> - 撤销</li>
          <li><kbd>Ctrl + Y</kbd> - 重做</li>
        </ul>

        <h4>段落操作</h4>
        <ul>
          <li><kbd>Tab</kbd> - 增加缩进</li>
          <li><kbd>Shift + Tab</kbd> - 减少缩进</li>
        </ul>

        <h4>其他功能</h4>
        <ul>
          <li>支持拖拽图片调整位置</li>
          <li>自动保存功能（每2秒保存一次）</li>
          <li>支持富文本粘贴</li>
        </ul>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showHelp = false">知道了</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'SimpleWordDesigner',
  data() {
    return {
      // 格式化状态
      fontFamily: 'Microsoft YaHei',
      fontSize: '14px',
      fontSizeNumber: 14,
      fontColor: '#000000',
      isBold: false,
      isItalic: false,
      isUnderline: false,
      isStrikethrough: false,
      lineHeight: 1.5,
      paragraphSpacing: 0,
      currentParagraphHasCustomFont: false,

      // 页面设置
      showPageSettings: false,
      showHelp: false,
      currentPage: 1,
      pageSettings: {
        paperSize: 'A4',
        orientation: 'portrait',
        marginTop: 25,
        marginBottom: 25,
        marginLeft: 30,
        marginRight: 30,
        showHeader: false,
        headerText: '',
        showFooter: false,
        footerText: '',
        showPageNumber: true,
        headerFooterFont: 'Microsoft YaHei',
        headerFooterFontSize: 12,
        headerFooterColor: '#666666'
      },

      // 文档内容
      documentContent: '',

      // 自动保存定时器
      autoSaveTimer: null,

      // 图片缩放相关
      selectedImage: null,
      isResizing: false,
      resizeData: null,

      // 文本选择状态保存
      savedSelection: null,

      // 纸张尺寸配置
      paperSizes: {
        A4: { width: '210mm', height: '297mm' },
        A3: { width: '297mm', height: '420mm' },
        Letter: { width: '8.5in', height: '11in' }
      }
    }
  },
  computed: {
    pageContainerStyle() {
      const size = this.paperSizes[this.pageSettings.paperSize] || this.paperSizes.A4;
      const isLandscape = this.pageSettings.orientation === 'landscape';

      return {
        width: isLandscape ? size.height : size.width,
        height: isLandscape ? size.width : size.height,
        margin: '20px auto',
        padding: `${this.pageSettings.marginTop}mm ${this.pageSettings.marginRight}mm ${this.pageSettings.marginBottom}mm ${this.pageSettings.marginLeft}mm`,
        backgroundColor: '#ffffff',
        boxShadow: '0 0 10px rgba(0,0,0,0.1)',
        minHeight: '800px'
      }
    },
    editorStyle() {
      return {
        fontFamily: this.fontFamily,
        fontSize: this.fontSize,
        color: this.fontColor,
        lineHeight: this.lineHeight,
        marginBottom: this.paragraphSpacing + 'px',
        minHeight: '600px',
        outline: 'none',
        border: 'none'
      }
    },
    headerFooterStyle() {
      return {
        fontFamily: this.pageSettings.headerFooterFont,
        fontSize: this.pageSettings.headerFooterFontSize + 'px',
        color: this.pageSettings.headerFooterColor,
        textAlign: 'center',
        padding: '10px 0',
        borderBottom: this.pageSettings.showHeader ? '1px solid #eee' : 'none',
        borderTop: this.pageSettings.showFooter ? '1px solid #eee' : 'none'
      }
    }
  },
  mounted() {
    this.initEditor();

    // 恢复自动保存的内容
    this.$nextTick(() => {
      this.restoreAutoSave();
    });
  },

  beforeDestroy() {
    // 清理定时器
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
    }
  },
  methods: {
    initEditor() {
      // 初始化编辑器
      this.$nextTick(() => {
        if (this.$refs.editor) {
          this.$refs.editor.focus();
        }
      });
    },

    // 文档操作
    newDocument() {
      if (confirm('确定要新建文档吗？当前内容将丢失。')) {
        this.$refs.editor.innerHTML = '<p>在此输入您的文档内容...</p>';
        this.documentContent = '';
      }
    },

    exportDocument() {
      // 获取文档内容
      const content = this.$refs.editor.innerHTML;
      const documentData = {
        content: content,
        pageSettings: this.pageSettings,
        title: '简单Word文档'
      };

      // 调用导出接口
      this.callExportAPI(documentData);
    },

    async callExportAPI(documentData) {
      try {
        this.$message.info('正在导出文档...');

        // 验证导出请求
        const validation = await this.validateExportRequest(documentData);
        if (!validation.success) {
          this.$message.error(validation.message);
          return;
        }

        // 调用导出API
        const { exportAndDownloadSimpleWord } = await import('@/api/word/simpleWordDesigner');
        const result = await exportAndDownloadSimpleWord(documentData);

        this.$message.success(`文档导出成功！文件大小: ${this.formatFileSize(result.size)}`);
        console.log('导出成功:', result);

      } catch (error) {
        console.error('导出失败:', error);
        let errorMessage = '文档导出失败';

        if (error.response) {
          // 服务器返回错误
          if (error.response.status === 400) {
            errorMessage = '请求参数错误';
          } else if (error.response.status === 500) {
            errorMessage = '服务器内部错误';
          } else {
            errorMessage = `导出失败 (${error.response.status})`;
          }
        } else if (error.message) {
          errorMessage += ': ' + error.message;
        }

        this.$message.error(errorMessage);
      }
    },

    async validateExportRequest(documentData) {
      try {
        // 基本验证
        if (!documentData.title || documentData.title.trim() === '') {
          return { success: false, message: '文档标题不能为空' };
        }

        if (!documentData.content || documentData.content.trim() === '') {
          return { success: false, message: '文档内容不能为空' };
        }

        // 调用后端验证API
        const { validateExportRequest } = await import('@/api/word/simpleWordDesigner');
        const response = await validateExportRequest(documentData);

        if (response.code === 200) {
          return { success: true };
        } else {
          return { success: false, message: response.msg || '验证失败' };
        }

      } catch (error) {
        console.warn('验证请求失败，跳过验证:', error);
        return { success: true }; // 验证失败时允许继续导出
      }
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    previewDocument() {
      // 打开预览窗口
      const content = this.$refs.editor.innerHTML;
      const previewWindow = window.open('', '_blank');
      previewWindow.document.write(`
        <html>
          <head>
            <title>文档预览</title>
            <style>
              body {
                font-family: ${this.fontFamily};
                font-size: ${this.fontSize};
                color: ${this.fontColor};
                line-height: ${this.lineHeight};
                margin: ${this.pageSettings.marginTop}mm ${this.pageSettings.marginRight}mm ${this.pageSettings.marginBottom}mm ${this.pageSettings.marginLeft}mm;
              }
              @media print {
                body { margin: 0; }
              }
            </style>
          </head>
          <body>
            ${this.pageSettings.showHeader ? '<div style="text-align: center; border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 20px;">' + this.pageSettings.headerText + '</div>' : ''}
            ${content}
            ${this.pageSettings.showFooter ? '<div style="text-align: center; border-top: 1px solid #eee; padding-top: 10px; margin-top: 20px;">' + this.pageSettings.footerText + (this.pageSettings.showPageNumber ? ' 第 ' + this.currentPage + ' 页' : '') + '</div>' : ''}
          </body>
        </html>
      `);
    },

    // 字体格式化
    applyFontFamily() {
      // 尝试恢复保存的选择状态
      this.restoreSelection();

      document.execCommand('fontName', false, this.fontFamily);

      // 清除保存的选择状态
      this.clearSavedSelection();
      this.focusEditor();
    },

    onFontSizeNumberChange(value) {
      if (value && value >= 8 && value <= 72) {
        this.fontSize = value + 'px';
        this.applyFontSize();
      }
    },

    onFontSizeSelectVisibleChange(visible) {
      if (visible) {
        // 下拉框打开时保存选择状态
        this.saveSelection();
      }
    },

    onFontFamilySelectVisibleChange(visible) {
      if (visible) {
        // 下拉框打开时保存选择状态
        this.saveSelection();
      }
    },

    applyFontSize() {
      // 保存当前选择状态
      this.saveSelection();

      const selection = window.getSelection();

      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);

        if (!range.collapsed) {
          // 有选中文本，只对选中的文本应用字体大小
          this.applyFontSizeToSelectedText();
        } else {
          // 没有选中文本，对当前行应用字体大小
          this.applyFontSizeToCurrentLine();
        }
      }

      // 恢复选择状态
      this.restoreSelection();
      this.clearSavedSelection();
      this.focusEditor();
    },

    applyFontSizeToSelectedText() {
      const selection = window.getSelection();
      if (selection.rangeCount === 0 || selection.isCollapsed) return;

      const range = selection.getRangeAt(0);
      const selectedText = range.toString();

      if (!selectedText) return;

      // 使用更智能的方式处理选中文本的字体大小
      this.smartApplyFontSizeToSelection(range, selectedText);
    },

    // 智能应用字体大小到选中文本
    smartApplyFontSizeToSelection(range, selectedText) {
      const selection = window.getSelection();

      // 尝试使用更精确的样式应用方式
      if (this.isSimpleSelection(range)) {
        this.applyFontSizeToSimpleSelection(range, selectedText);
      } else {
        this.applyFontSizeToComplexSelection(range, selectedText);
      }
    },

    // 判断是否为简单选择（在同一父元素内）
    isSimpleSelection(range) {
      return range.startContainer === range.endContainer;
    },

    // 应用字体大小到简单选择
    applyFontSizeToSimpleSelection(range, selectedText) {
      const selection = window.getSelection();
      const container = range.startContainer;

      if (container.nodeType === Node.TEXT_NODE) {
        // 文本节点选择
        this.formatTextSelection(container, range.startOffset, range.endOffset, selectedText);
      } else {
        // 元素节点选择
        this.applyFontSizeToElementSelection(container, selectedText);
      }
    },

    // 格式化文本选择
    formatTextSelection(textNode, startOffset, endOffset, selectedText) {
      const selection = window.getSelection();
      const parent = textNode.parentNode;

      try {
        // 获取现有样式
        const existingStyles = this.getExistingStyles(textNode);

        // 创建新的span元素
        const span = document.createElement('span');
        span.style.fontSize = this.fontSize;

        // 应用现有样式
        Object.assign(span.style, existingStyles);

        // 分割文本节点
        const beforeText = textNode.textContent.substring(0, startOffset);
        const afterText = textNode.textContent.substring(endOffset);

        // 替换选中的文本
        span.textContent = selectedText;

        // 重建DOM结构
        if (beforeText) {
          const beforeNode = document.createTextNode(beforeText);
          parent.insertBefore(beforeNode, textNode);
        }

        parent.insertBefore(span, textNode);

        if (afterText) {
          const afterNode = document.createTextNode(afterText);
          parent.insertBefore(afterNode, textNode);
        }

        // 移除原始文本节点
        parent.removeChild(textNode);

        // 恢复选择状态
        const newRange = document.createRange();
        newRange.selectNodeContents(span);
        selection.removeAllRanges();
        selection.addRange(newRange);

      } catch (error) {
        console.warn('格式化文本选择失败:', error);
        this.fallbackToExecCommand();
      }
    },

    // 获取现有样式
    getExistingStyles(node) {
      const styles = {};
      const parent = node.parentNode;

      if (parent && parent.nodeType === Node.ELEMENT_NODE) {
        const computedStyle = window.getComputedStyle(parent);
        const styleProperties = ['fontFamily', 'color', 'fontWeight', 'fontStyle', 'textDecoration'];

        styleProperties.forEach(prop => {
          const value = computedStyle[prop];
          if (value && value !== 'initial' && value !== 'none') {
            styles[prop] = value;
          }
        });
      }

      return styles;
    },

    // 应用字体大小到元素选择
    applyFontSizeToElementSelection(element, selectedText) {
      const selection = window.getSelection();

      try {
        // 如果元素已经有样式，只修改字体大小
        if (element.style && Object.keys(element.style).length > 0) {
          element.style.fontSize = this.fontSize;
        } else {
          // 创建新的格式化元素
          const span = document.createElement('span');
          span.style.fontSize = this.fontSize;
          span.textContent = selectedText;

          // 替换选中内容
          const range = selection.getRangeAt(0);
          range.deleteContents();
          range.insertNode(span);

          // 恢复选择状态
          const newRange = document.createRange();
          newRange.selectNodeContents(span);
          selection.removeAllRanges();
          selection.addRange(newRange);
        }
      } catch (error) {
        console.warn('应用字体大小到元素选择失败:', error);
        this.fallbackToExecCommand();
      }
    },

    // 应用字体大小到复杂选择（跨多个元素）
    applyFontSizeToComplexSelection(range, selectedText) {
      try {
        // 对于复杂选择，使用execCommand作为主要方式
        this.fallbackToExecCommand();
      } catch (error) {
        console.warn('应用字体大小到复杂选择失败:', error);
      }
    },

    // 回退到execCommand方式
    fallbackToExecCommand() {
      const selection = window.getSelection();

      try {
        // 使用execCommand设置字体大小
        document.execCommand('fontSize', false, '7');

        // 将font标签转换为span并设置正确的字体大小
        setTimeout(() => {
          const fontTags = this.$refs.editor.querySelectorAll('font[size="7"]');
          fontTags.forEach(font => {
            const span = document.createElement('span');
            span.style.fontSize = this.fontSize;
            span.innerHTML = font.innerHTML;

            // 复制其他样式
            if (font.style.cssText) {
              const existingStyles = font.style.cssText.split(';').filter(style =>
                style.trim() && !style.includes('font-size')
              );
              span.style.cssText = existingStyles.join(';') + '; font-size: ' + this.fontSize;
            }

            font.parentNode.replaceChild(span, font);
          });
        }, 10);
      } catch (error) {
        console.warn('execCommand方式失败:', error);
      }
    },

    applyFontSizeToCurrentLine() {
      const selection = window.getSelection();
      if (selection.rangeCount === 0) return;

      const range = selection.getRangeAt(0);
      const startContainer = range.startContainer;

      // 情况1：光标在文本节点中
      if (startContainer.nodeType === Node.TEXT_NODE) {
        this.applyFontSizeToTextNode(startContainer, range.startOffset);
        return;
      }

      // 情况2：光标在元素节点中
      if (startContainer.nodeType === Node.ELEMENT_NODE) {
        this.applyFontSizeToElementNode(startContainer, range.startOffset);
        return;
      }
    },

    // 对文本节点应用字体大小
    applyFontSizeToTextNode(textNode, offset) {
      const parentElement = textNode.parentElement;
      const textContent = textNode.textContent;

      // 如果文本为空，在父元素上设置字体大小
      if (!textContent || textContent.trim() === '') {
        parentElement.style.fontSize = this.fontSize;
        return;
      }

      // 如果光标在文本开头或结尾，创建新的格式化节点
      if (offset === 0 || offset === textContent.length) {
        this.createFormattedSpanAtCursor(textNode, offset);
        return;
      }

      // 如果光标在文本中间，分割文本节点
      this.splitAndFormatTextNode(textNode, offset);
    },

    // 对元素节点应用字体大小
    applyFontSizeToElementNode(element, offset) {
      // 如果元素已经是span且有内容，直接设置字体大小
      if (element.tagName === 'SPAN' && element.textContent.trim() !== '') {
        element.style.fontSize = this.fontSize;
        return;
      }

      // 如果元素是空的或者不是span，在其中创建格式化的span
      this.createFormattedSpanInElement(element, offset);
    },

    // 在光标位置创建格式化的span
    createFormattedSpanAtCursor(textNode, offset) {
      const selection = window.getSelection();
      const range = document.createRange();

      const span = document.createElement('span');
      span.style.fontSize = this.fontSize;
      span.textContent = '\u00A0'; // 不换行空格

      try {
        range.setStart(textNode, offset);
        range.insertNode(span);

        // 将光标移到span后面
        range.setStartAfter(span);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      } catch (error) {
        console.warn('创建格式化span失败:', error);
        // 回退：直接在父元素上设置字体大小
        textNode.parentElement.style.fontSize = this.fontSize;
      }
    },

    // 在元素中创建格式化的span
    createFormattedSpanInElement(element, offset) {
      const selection = window.getSelection();
      const range = document.createRange();

      const span = document.createElement('span');
      span.style.fontSize = this.fontSize;
      span.textContent = '\u00A0';

      try {
        // 如果元素有子节点，根据offset插入
        if (element.childNodes.length > 0 && offset < element.childNodes.length) {
          range.setStartBefore(element.childNodes[offset]);
        } else {
          range.selectNodeContents(element);
          range.collapse(false);
        }

        range.insertNode(span);

        // 将光标移到span后面
        range.setStartAfter(span);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      } catch (error) {
        console.warn('在元素中创建格式化span失败:', error);
        // 回退：直接在元素上设置字体大小
        element.style.fontSize = this.fontSize;
      }
    },

    // 分割文本节点并格式化
    splitAndFormatTextNode(textNode, offset) {
      const selection = window.getSelection();
      const range = document.createRange();

      try {
        // 分割文本节点
        const beforeText = textNode.textContent.substring(0, offset);
        const afterText = textNode.textContent.substring(offset);

        // 创建新的span用于格式化
        const span = document.createElement('span');
        span.style.fontSize = this.fontSize;
        span.textContent = '\u00A0';

        // 替换原始文本节点
        const parent = textNode.parentNode;

        if (beforeText) {
          const beforeNode = document.createTextNode(beforeText);
          parent.insertBefore(beforeNode, textNode);
        }

        parent.insertBefore(span, textNode);

        if (afterText) {
          const afterNode = document.createTextNode(afterText);
          parent.insertBefore(afterNode, textNode);
        }

        // 移除原始文本节点
        parent.removeChild(textNode);

        // 将光标移到span后面
        range.setStartAfter(span);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      } catch (error) {
        console.warn('分割文本节点失败:', error);
        // 回退：直接在父元素上设置字体大小
        textNode.parentElement.style.fontSize = this.fontSize;
      }
    },





    applyFontColor() {
      // 尝试恢复保存的选择状态
      this.restoreSelection();

      document.execCommand('foreColor', false, this.fontColor);

      // 清除保存的选择状态
      this.clearSavedSelection();
      this.focusEditor();
    },

    applyLineHeight() {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let element = range.commonAncestorContainer;
        if (element.nodeType === Node.TEXT_NODE) {
          element = element.parentElement;
        }

        // 找到段落元素
        while (element && element.tagName !== 'P' && element.tagName !== 'DIV') {
          element = element.parentElement;
        }

        if (element) {
          element.style.lineHeight = this.lineHeight;
        }
      }
      this.focusEditor();
    },

    applyParagraphSpacing() {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let element = range.commonAncestorContainer;
        if (element.nodeType === Node.TEXT_NODE) {
          element = element.parentElement;
        }

        // 找到段落元素
        while (element && element.tagName !== 'P' && element.tagName !== 'DIV') {
          element = element.parentElement;
        }

        if (element) {
          element.style.marginBottom = this.paragraphSpacing + 'px';
        }
      }
      this.focusEditor();
    },

    // 格式化按钮
    toggleBold() {
      this.restoreSelection();
      document.execCommand('bold');
      this.clearSavedSelection();
      this.updateFormatState();
      this.focusEditor();
    },

    toggleItalic() {
      this.restoreSelection();
      document.execCommand('italic');
      this.clearSavedSelection();
      this.updateFormatState();
      this.focusEditor();
    },

    toggleUnderline() {
      this.restoreSelection();
      document.execCommand('underline');
      this.clearSavedSelection();
      this.updateFormatState();
      this.focusEditor();
    },

    toggleStrikethrough() {
      this.restoreSelection();
      document.execCommand('strikeThrough');
      this.clearSavedSelection();
      this.updateFormatState();
      this.focusEditor();
    },

    // 对齐方式
    setAlignment(align) {
      this.restoreSelection();
      const commands = {
        left: 'justifyLeft',
        center: 'justifyCenter',
        right: 'justifyRight'
      };
      document.execCommand(commands[align]);
      this.clearSavedSelection();
      this.focusEditor();
    },

    // 缩进
    increaseIndent() {
      document.execCommand('indent');
      this.focusEditor();
    },

    decreaseIndent() {
      document.execCommand('outdent');
      this.focusEditor();
    },

    // 更新格式状态
    updateFormatState() {
      this.isBold = document.queryCommandState('bold');
      this.isItalic = document.queryCommandState('italic');
      this.isUnderline = document.queryCommandState('underline');
      this.isStrikethrough = document.queryCommandState('strikeThrough');

      // 更新字体大小显示
      this.updateFontSizeDisplay();

      // 更新段落格式状态
      this.updateParagraphFormatState();
    },

    updateFontSizeDisplay() {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let element = range.commonAncestorContainer;

        // 如果是文本节点，获取其父元素
        if (element.nodeType === Node.TEXT_NODE) {
          element = element.parentElement;
        }

        // 获取计算后的字体大小
        if (element && element.nodeType === Node.ELEMENT_NODE) {
          const computedStyle = window.getComputedStyle(element);
          const currentFontSize = computedStyle.fontSize;

          if (currentFontSize) {
            const sizeValue = parseInt(currentFontSize);
            if (sizeValue && sizeValue !== this.fontSizeNumber) {
              this.fontSizeNumber = sizeValue;
              this.fontSize = sizeValue + 'px';
            }
          }
        }
      }
    },

    updateParagraphFormatState() {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let element = range.commonAncestorContainer;

        // 如果是文本节点，获取其父元素
        if (element.nodeType === Node.TEXT_NODE) {
          element = element.parentElement;
        }

        // 找到段落元素
        while (element && element !== this.$refs.editor &&
               !['P', 'DIV', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(element.tagName)) {
          element = element.parentElement;
        }

        // 检查段落是否有自定义字体大小
        if (element && element !== this.$refs.editor) {
          const hasCustomFontSize = element.style.fontSize ||
                                   element.querySelector('[style*="font-size"]') ||
                                   element.querySelector('span[style*="font-size"]');
          this.currentParagraphHasCustomFont = !!hasCustomFontSize;
        } else {
          this.currentParagraphHasCustomFont = false;
        }
      }
    },

    focusEditor() {
      this.$nextTick(() => {
        if (this.$refs.editor) {
          this.$refs.editor.focus();
        }
      });
    },

    // 保存当前选择状态
    saveSelection() {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        this.savedSelection = {
          range: range.cloneRange(),
          hasSelection: !range.collapsed,
          startContainer: range.startContainer,
          startOffset: range.startOffset,
          endContainer: range.endContainer,
          endOffset: range.endOffset
        };
      } else {
        this.savedSelection = null;
      }
    },

    // 恢复选择状态
    restoreSelection() {
      if (this.savedSelection && this.savedSelection.range) {
        try {
          const selection = window.getSelection();
          selection.removeAllRanges();

          // 尝试恢复原始范围
          const restoredRange = this.savedSelection.range.cloneRange();
          selection.addRange(restoredRange);

          return this.savedSelection.hasSelection;
        } catch (error) {
          console.warn('恢复选择状态失败:', error);
          return false;
        }
      }
      return false;
    },

    // 清除保存的选择状态
    clearSavedSelection() {
      this.savedSelection = null;
    },

    // 图片处理
    handleImageUpload(file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        this.insertImage(e.target.result);
      };
      reader.readAsDataURL(file);
      return false; // 阻止默认上传
    },

    insertImage(src) {
      const img = document.createElement('img');
      img.src = src;
      img.style.maxWidth = '100%';
      img.style.height = 'auto';
      img.style.cursor = 'move';
      img.draggable = true;

      // 添加图片事件监听
      img.addEventListener('click', this.selectImage);
      img.addEventListener('dragstart', this.handleImageDragStart);
      img.addEventListener('dragend', this.handleImageDragEnd);

      // 插入到当前光标位置
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.insertNode(img);
        range.collapse(false);
      } else {
        this.$refs.editor.appendChild(img);
      }

      this.focusEditor();
    },

    selectImage(event) {
      // 清除其他选中的图片
      const images = this.$refs.editor.querySelectorAll('img');
      images.forEach(img => img.classList.remove('selected'));

      // 选中当前图片
      event.target.classList.add('selected');

      // 添加调整大小的控制点
      this.addResizeHandles(event.target);
    },

    addResizeHandles(img) {
      // 移除现有的控制点
      this.removeResizeHandles();

      // 存储当前选中的图片
      this.selectedImage = img;

      // 创建调整大小的控制点
      const handles = ['nw', 'ne', 'sw', 'se'];
      handles.forEach(position => {
        const handle = document.createElement('div');
        handle.className = `resize-handle resize-${position}`;
        handle.style.cssText = `
          position: absolute;
          width: 10px;
          height: 10px;
          background: #409EFF;
          border: 2px solid #fff;
          border-radius: 50%;
          cursor: ${position}-resize;
          z-index: 1000;
        `;

        // 设置位置
        this.updateHandlePosition(handle, img, position);

        // 添加鼠标事件
        handle.addEventListener('mousedown', (e) => this.startResize(e, position, img));

        this.$refs.editor.appendChild(handle);
      });

      // 添加图片周围的边框
      img.style.border = '2px dashed #409EFF';
    },

    updateHandlePosition(handle, img, position) {
      const rect = img.getBoundingClientRect();
      const containerRect = this.$refs.editor.getBoundingClientRect();
      const scrollTop = this.$refs.editor.scrollTop || 0;
      const scrollLeft = this.$refs.editor.scrollLeft || 0;

      switch(position) {
        case 'nw':
          handle.style.top = (rect.top - containerRect.top + scrollTop - 5) + 'px';
          handle.style.left = (rect.left - containerRect.left + scrollLeft - 5) + 'px';
          break;
        case 'ne':
          handle.style.top = (rect.top - containerRect.top + scrollTop - 5) + 'px';
          handle.style.left = (rect.right - containerRect.left + scrollLeft - 5) + 'px';
          break;
        case 'sw':
          handle.style.top = (rect.bottom - containerRect.top + scrollTop - 5) + 'px';
          handle.style.left = (rect.left - containerRect.left + scrollLeft - 5) + 'px';
          break;
        case 'se':
          handle.style.top = (rect.bottom - containerRect.top + scrollTop - 5) + 'px';
          handle.style.left = (rect.right - containerRect.left + scrollLeft - 5) + 'px';
          break;
      }
    },

    startResize(event, position, img) {
      event.preventDefault();
      event.stopPropagation();

      this.isResizing = true;
      this.resizeData = {
        startX: event.clientX,
        startY: event.clientY,
        startWidth: img.offsetWidth,
        startHeight: img.offsetHeight,
        position: position,
        img: img
      };

      // 添加缩放样式
      img.classList.add('resizing');

      // 添加全局鼠标事件
      document.addEventListener('mousemove', this.handleResize);
      document.addEventListener('mouseup', this.stopResize);

      // 防止文本选择
      document.body.style.userSelect = 'none';
    },

    handleResize(event) {
      if (!this.isResizing || !this.resizeData) return;

      const { startX, startY, startWidth, startHeight, position, img } = this.resizeData;
      const deltaX = event.clientX - startX;
      const deltaY = event.clientY - startY;

      // 计算原始宽高比
      const aspectRatio = startWidth / startHeight;

      let newWidth = startWidth;
      let newHeight = startHeight;

      // 四角缩放都保持等比例
      switch(position) {
        case 'se': // 右下角
          // 以较大的变化量为准，保持等比例
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            newWidth = startWidth + deltaX;
            newHeight = newWidth / aspectRatio;
          } else {
            newHeight = startHeight + deltaY;
            newWidth = newHeight * aspectRatio;
          }
          break;
        case 'sw': // 左下角
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            newWidth = startWidth - deltaX;
            newHeight = newWidth / aspectRatio;
          } else {
            newHeight = startHeight + deltaY;
            newWidth = newHeight * aspectRatio;
          }
          break;
        case 'ne': // 右上角
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            newWidth = startWidth + deltaX;
            newHeight = newWidth / aspectRatio;
          } else {
            newHeight = startHeight - deltaY;
            newWidth = newHeight * aspectRatio;
          }
          break;
        case 'nw': // 左上角
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            newWidth = startWidth - deltaX;
            newHeight = newWidth / aspectRatio;
          } else {
            newHeight = startHeight - deltaY;
            newWidth = newHeight * aspectRatio;
          }
          break;
      }

      // 限制最小尺寸，保持宽高比
      const minSize = 20;
      if (newWidth < minSize) {
        newWidth = minSize;
        newHeight = newWidth / aspectRatio;
      }
      if (newHeight < minSize) {
        newHeight = minSize;
        newWidth = newHeight * aspectRatio;
      }

      // 限制最大尺寸（不超过编辑器宽度）
      const editorWidth = this.$refs.editor.offsetWidth - 40; // 留一些边距
      if (newWidth > editorWidth) {
        newWidth = editorWidth;
        newHeight = newWidth / aspectRatio;
      }

      // 应用新尺寸
      img.style.width = newWidth + 'px';
      img.style.height = newHeight + 'px';

      // 更新控制点位置
      this.updateAllHandlePositions(img);
    },

    stopResize() {
      if (this.resizeData && this.resizeData.img) {
        // 移除缩放样式
        this.resizeData.img.classList.remove('resizing');
      }

      this.isResizing = false;
      this.resizeData = null;

      // 移除全局事件监听
      document.removeEventListener('mousemove', this.handleResize);
      document.removeEventListener('mouseup', this.stopResize);

      // 恢复文本选择
      document.body.style.userSelect = '';

      // 触发内容变化事件
      this.handleContentChange({ target: this.$refs.editor });
    },

    updateAllHandlePositions(img) {
      const handles = this.$refs.editor.querySelectorAll('.resize-handle');
      handles.forEach(handle => {
        const position = handle.className.split(' ')[1].replace('resize-', '');
        this.updateHandlePosition(handle, img, position);
      });
    },

    removeResizeHandles() {
      const handles = this.$refs.editor.querySelectorAll('.resize-handle');
      handles.forEach(handle => handle.remove());

      // 移除图片边框
      if (this.selectedImage) {
        this.selectedImage.style.border = '';
        this.selectedImage = null;
      }

      // 移除所有图片的选中状态
      const images = this.$refs.editor.querySelectorAll('img');
      images.forEach(img => {
        img.classList.remove('selected');
        img.style.border = '';
      });
    },

    handleImageDragStart(event) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/html', event.target.outerHTML);
    },

    handleImageDragEnd(event) {
      // 拖拽结束后的处理
    },

    // 编辑器点击处理
    handleEditorClick(event) {
      // 如果点击的不是图片，则取消图片选择
      if (event.target.tagName !== 'IMG' && !event.target.classList.contains('resize-handle')) {
        this.removeResizeHandles();
      }
      this.updateFormatState();
    },

    // 内容处理
    handleContentChange(event) {
      this.documentContent = event.target.innerHTML;

      // 自动保存到本地存储
      this.autoSave();
    },

    handlePaste(event) {
      // 处理粘贴事件，支持富文本和纯文本
      event.preventDefault();

      // 尝试获取HTML内容
      let htmlData = event.clipboardData.getData('text/html');
      let textData = event.clipboardData.getData('text/plain');

      if (htmlData) {
        // 清理HTML内容，移除危险标签
        htmlData = this.sanitizeHtml(htmlData);
        document.execCommand('insertHTML', false, htmlData);
      } else if (textData) {
        // 插入纯文本
        document.execCommand('insertText', false, textData);
      }

      this.updateFormatState();
    },

    sanitizeHtml(html) {
      // 创建临时DOM元素来清理HTML
      const temp = document.createElement('div');
      temp.innerHTML = html;

      // 移除script标签
      const scripts = temp.querySelectorAll('script');
      scripts.forEach(script => script.remove());

      // 移除危险的事件属性
      const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur'];
      const allElements = temp.querySelectorAll('*');
      allElements.forEach(element => {
        dangerousAttrs.forEach(attr => {
          if (element.hasAttribute(attr)) {
            element.removeAttribute(attr);
          }
        });
      });

      return temp.innerHTML;
    },

    // 自动保存功能
    autoSave() {
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer);
      }

      this.autoSaveTimer = setTimeout(() => {
        try {
          const saveData = {
            content: this.documentContent,
            pageSettings: this.pageSettings,
            timestamp: new Date().toISOString()
          };

          localStorage.setItem('simpleWordDesigner_autoSave', JSON.stringify(saveData));
          console.log('文档已自动保存');
        } catch (error) {
          console.warn('自动保存失败:', error);
        }
      }, 2000); // 2秒后保存
    },

    // 恢复自动保存的内容
    restoreAutoSave() {
      try {
        const savedData = localStorage.getItem('simpleWordDesigner_autoSave');
        if (savedData) {
          const data = JSON.parse(savedData);

          // 询问用户是否恢复
          this.$confirm('检测到未保存的文档内容，是否恢复？', '恢复文档', {
            confirmButtonText: '恢复',
            cancelButtonText: '忽略',
            type: 'info'
          }).then(() => {
            if (data.content) {
              this.$refs.editor.innerHTML = data.content;
              this.documentContent = data.content;
            }
            if (data.pageSettings) {
              this.pageSettings = { ...this.pageSettings, ...data.pageSettings };
            }
            this.$message.success('文档内容已恢复');
          }).catch(() => {
            // 用户选择忽略，清除自动保存数据
            localStorage.removeItem('simpleWordDesigner_autoSave');
          });
        }
      } catch (error) {
        console.warn('恢复自动保存失败:', error);
      }
    },

    // 清除自动保存
    clearAutoSave() {
      localStorage.removeItem('simpleWordDesigner_autoSave');
    },

    // 键盘快捷键处理
    handleKeyDown(event) {
      // Ctrl/Cmd + 组合键
      if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
          case 'b':
            // Ctrl+B 加粗
            event.preventDefault();
            this.toggleBold();
            break;
          case 'i':
            // Ctrl+I 斜体
            event.preventDefault();
            this.toggleItalic();
            break;
          case 'u':
            // Ctrl+U 下划线
            event.preventDefault();
            this.toggleUnderline();
            break;
          case 's':
            // Ctrl+S 保存（导出）
            event.preventDefault();
            this.exportDocument();
            break;
          case 'p':
            // Ctrl+P 预览
            event.preventDefault();
            this.previewDocument();
            break;
          case 'z':
            // Ctrl+Z 撤销（浏览器默认行为）
            // 不阻止默认行为，让浏览器处理
            break;
          case 'y':
            // Ctrl+Y 重做（浏览器默认行为）
            // 不阻止默认行为，让浏览器处理
            break;
          case '=':
          case '+':
            // Ctrl+= 或 Ctrl++ 增大字体
            event.preventDefault();
            this.saveSelection(); // 保存当前选择状态
            this.increaseFontSize();
            break;
          case '-':
            // Ctrl+- 减小字体
            event.preventDefault();
            this.saveSelection(); // 保存当前选择状态
            this.decreaseFontSize();
            break;
        }
      }

      // 其他快捷键
      switch (event.key) {
        case 'Tab':
          // Tab键增加缩进
          if (!event.shiftKey) {
            event.preventDefault();
            this.increaseIndent();
          } else {
            // Shift+Tab减少缩进
            event.preventDefault();
            this.decreaseIndent();
          }
          break;
      }
    },

    // 字体大小调整方法
    increaseFontSize() {
      this.saveSelection(); // 保存当前选择状态
      const currentSize = parseInt(this.fontSize);
      const fontSizes = [8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 42, 48, 54, 60, 72];
      const currentIndex = fontSizes.indexOf(currentSize);

      if (currentIndex < fontSizes.length - 1) {
        const newSize = fontSizes[currentIndex + 1];
        this.fontSize = newSize + 'px';
        this.fontSizeNumber = newSize;
        this.applyFontSize();
      }
    },

    decreaseFontSize() {
      this.saveSelection(); // 保存当前选择状态
      const currentSize = parseInt(this.fontSize);
      const fontSizes = [8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 42, 48, 54, 60, 72];
      const currentIndex = fontSizes.indexOf(currentSize);

      if (currentIndex > 0) {
        const newSize = fontSizes[currentIndex - 1];
        this.fontSize = newSize + 'px';
        this.fontSizeNumber = newSize;
        this.applyFontSize();
      }
    },

    // 页面设置
    applyPageSettings() {
      this.showPageSettings = false;
      this.$message.success('页面设置已应用');
    },

    applyPaperSize() {
      // 纸张大小改变时的处理
    },

    applyOrientation() {
      // 页面方向改变时的处理
    },

    updateHeader(event) {
      this.pageSettings.headerText = event.target.textContent;
    },

    updateFooter(event) {
      this.pageSettings.footerText = event.target.textContent;
    }
  }
}
</script>

<style scoped>
.word-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.toolbar {
  background: #ffffff;
  padding: 10px;
  border-bottom: 1px solid #e6e6e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.editor-container {
  flex: 1;
  overflow: auto;
  position: relative;
}

.ruler {
  height: 20px;
  background: #f0f0f0;
  border-bottom: 1px solid #ddd;
  position: relative;
}

.ruler-numbers {
  position: relative;
  height: 100%;
}

.ruler-numbers span {
  position: absolute;
  top: 2px;
  font-size: 10px;
  color: #666;
}

.page-container {
  position: relative;
  background: white;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.page-header,
.page-footer {
  min-height: 30px;
  outline: none;
}

.editor-content {
  min-height: 600px;
  outline: none;
  word-wrap: break-word;
}

.editor-content p {
  margin: 0 0 10px 0;
}

.is-active {
  background-color: #409EFF !important;
  color: white !important;
}

/* 图片样式 */
.editor-content img {
  max-width: 100%;
  height: auto;
  cursor: move;
  border: 2px dashed transparent;
}

.editor-content img:hover {
  border-color: #409EFF;
}

.editor-content img.selected {
  border-color: #409EFF;
}

/* 调整大小控制点 */
.resize-handle {
  position: absolute;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

.resize-handle:hover {
  transform: scale(1.2);
  background: #66b1ff !important;
}

.resize-nw { cursor: nw-resize; }
.resize-ne { cursor: ne-resize; }
.resize-sw { cursor: sw-resize; }
.resize-se { cursor: se-resize; }

/* 图片选中状态 */
.editor-content img.selected {
  border: 2px dashed #409EFF !important;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

/* 图片缩放时的样式 */
.editor-content img.resizing {
  opacity: 0.8;
  transition: none;
}

/* 拖拽样式 */
.editor-content {
  position: relative;
}

.editor-content [draggable="true"] {
  user-select: none;
}

/* 工具栏按钮样式 */
.toolbar .el-button-group .el-button {
  margin: 0;
}

.toolbar .el-select {
  width: 100%;
}

.toolbar .el-input-number {
  width: 100%;
}

/* 页面容器响应式 */
@media (max-width: 768px) {
  .page-container {
    width: 100% !important;
    margin: 10px !important;
    padding: 10px !important;
  }

  .toolbar {
    padding: 5px;
  }

  .toolbar .el-col {
    margin-bottom: 5px;
  }
}

/* 打印样式 */
@media print {
  .toolbar,
  .ruler {
    display: none;
  }

  .page-container {
    box-shadow: none;
    margin: 0;
  }

  .resize-handle {
    display: none;
  }
}

/* 帮助对话框样式 */
.help-content {
  line-height: 1.6;
}

.help-content h4 {
  color: #409EFF;
  margin: 15px 0 10px 0;
  font-size: 14px;
}

.help-content ul {
  margin: 0 0 15px 0;
  padding-left: 20px;
}

.help-content li {
  margin: 5px 0;
  font-size: 13px;
}

.help-content kbd {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 6px;
  font-family: monospace;
  font-size: 12px;
  color: #333;
}
</style>
