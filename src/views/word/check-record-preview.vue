<template>
  <div class="check-record-preview">
    <div class="preview-header">
      <h2>检验记录表预览</h2>
      <div class="header-actions">
        <div class="search-form">
          <label>车辆ID:</label>
          <input
            v-model="searchCarId"
            type="text"
            placeholder="请输入车辆ID"
            class="search-input"
            @keyup.enter="loadPreviewData"
          >
          <button @click="loadPreviewData" class="search-btn">查询</button>
        </div>
        <div class="action-buttons">
          <button @click="exportToWord" class="export-btn" :disabled="!hasData">导出Word</button>
          <button @click="refreshData" class="refresh-btn">刷新</button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <div class="loading-spinner"></div>
      <p>正在加载数据...</p>
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="!hasData" class="no-data">
      <div class="no-data-icon">📋</div>
      <p>暂无数据</p>
      <p class="no-data-tip">请输入车辆ID查询对应的检验记录表</p>
    </div>

    <!-- 预览内容 -->
    <div v-else class="preview-content">
      <div class="preview-info">
        <div class="info-item">
          <span class="label">车辆ID:</span>
          <span class="value">{{ currentDesign.carId }}</span>
        </div>
        <div class="info-item">
          <span class="label">表格标题:</span>
          <span class="value">{{ currentDesign.title }}</span>
        </div>
        <div class="info-item">
          <span class="label">创建时间:</span>
          <span class="value">{{ formatDate(currentDesign.createTime) }}</span>
        </div>
        <div class="info-item">
          <span class="label">更新时间:</span>
          <span class="value">{{ formatDate(currentDesign.updateTime) }}</span>
        </div>
      </div>

      <!-- 表格预览 -->
      <div class="table-preview">
        <TableContainer
          ref="tableContainer"
          :table-width="'100%'"
          :table-height="'600px'"
          :data-rows="previewTableData"
        />
      </div>
    </div>

    <!-- 操作结果提示 -->
    <div v-if="operationResult" class="operation-result" :class="operationResult.success ? 'success' : 'error'">
      {{ operationResult.message }}
    </div>
  </div>
</template>

<script>
import TableContainer from '@/components/TableContainer.vue'
import { getDesignWordByCarId } from '@/api/word/designWord'

export default {
  name: 'CheckRecordPreview',
  components: {
    TableContainer
  },
  data() {
    return {
      // 搜索车辆ID
      searchCarId: '0822',

      // 加载状态
      loading: false,

      // 当前设计数据
      currentDesign: null,

      // 预览表格数据
      previewTableData: [],

      // 表格配置
      tableConfig: null,

      // 操作结果
      operationResult: null
    }
  },
  computed: {
    hasData() {
      return this.currentDesign && this.previewTableData.length > 0
    }
  },
  mounted() {
    // 初始加载数据
    if (this.searchCarId) {
      this.loadPreviewData()
    }
  },
  methods: {
    /**
     * 加载预览数据
     */
    async loadPreviewData() {
      if (!this.searchCarId || this.searchCarId.trim() === '') {
        this.showOperationResult(false, '请输入车辆ID')
        return
      }

      this.loading = true
      try {
        const response = await getDesignWordByCarId(this.searchCarId.trim())

        if (response.code === 200 && response.data) {
          this.currentDesign = response.data
          await this.parseAndRenderTable()
          this.showOperationResult(true, '数据加载成功')
        } else {
          this.currentDesign = null
          this.previewTableData = []
          this.showOperationResult(false, '未找到对应车辆的检验记录表')
        }
      } catch (error) {
        console.error('加载预览数据失败:', error)
        this.showOperationResult(false, '加载数据失败')
        this.currentDesign = null
        this.previewTableData = []
      } finally {
        this.loading = false
      }
    },

    /**
     * 解析并渲染表格
     */
    async parseAndRenderTable() {
      try {
        // 解析表格配置
        if (this.currentDesign.tableConfig) {
          this.tableConfig = JSON.parse(this.currentDesign.tableConfig)
        }

        // 解析表格数据
        if (this.currentDesign.tableData) {
          const tableData = JSON.parse(this.currentDesign.tableData)
          if (tableData && Array.isArray(tableData)) {
            this.previewTableData = tableData
          }
        }

        // 等待DOM更新后应用配置
        await this.$nextTick()
        this.applyTableConfig()

      } catch (error) {
        console.error('解析表格数据失败:', error)
        this.showOperationResult(false, '表格数据解析失败')
      }
    },

    /**
     * 应用表格配置
     */
    applyTableConfig() {
      const tableContainer = this.$refs.tableContainer
      if (tableContainer && this.tableConfig) {
        // 设置动态表头配置
        tableContainer.setDynamicHeaderConfig(
          true,
          {
            headers: this.tableConfig.headers,
            merges: this.tableConfig.headerMerges
          },
          this.tableConfig.headerWidthConfig,
          this.tableConfig.verticalHeadersConfig
        )

        // 设置表格数据
        tableContainer.dataRows = this.previewTableData
      }
    },

    /**
     * 刷新数据
     */
    refreshData() {
      this.loadPreviewData()
    },

    /**
     * 导出Word文档
     */
    async exportToWord() {
      if (!this.hasData) {
        this.showOperationResult(false, '没有可导出的数据')
        return
      }

      try {
        this.showOperationResult(true, '正在准备导出...')

        const tableContainer = this.$refs.tableContainer
        if (!tableContainer) {
          this.showOperationResult(false, '表格组件未找到')
          return
        }

        // 调用表格组件的导出功能
        if (typeof tableContainer.exportToWord === 'function') {
          await tableContainer.exportToWord()
          this.showOperationResult(true, 'Word文档导出成功')
        } else {
          this.showOperationResult(false, '导出功能不可用')
        }

      } catch (error) {
        console.error('导出Word失败:', error)
        this.showOperationResult(false, '导出Word失败')
      }
    },

    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString) return '-'
      try {
        const date = new Date(dateString)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      } catch (error) {
        return dateString
      }
    },

    /**
     * 显示操作结果
     */
    showOperationResult(success, message) {
      this.operationResult = { success, message }
      setTimeout(() => {
        this.operationResult = null
      }, 3000)
    }
  }
}
</script>

<style scoped>
.check-record-preview {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.preview-header {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-header h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 24px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-form label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.search-btn, .export-btn, .refresh-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-btn {
  background: #007bff;
  color: white;
}

.search-btn:hover {
  background: #0056b3;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.export-btn {
  background: #28a745;
  color: white;
}

.export-btn:hover:not(:disabled) {
  background: #218838;
}

.export-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.refresh-btn {
  background: #17a2b8;
  color: white;
}

.refresh-btn:hover {
  background: #138496;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* 无数据状态 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-data-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-data p {
  color: #666;
  font-size: 16px;
  margin: 10px 0;
  text-align: center;
}

.no-data-tip {
  font-size: 14px;
  color: #999;
}

/* 预览内容 */
.preview-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-item .label {
  font-weight: 500;
  color: #333;
  min-width: 80px;
}

.info-item .value {
  color: #666;
  flex: 1;
}

.table-preview {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

/* 操作结果提示 */
.operation-result {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

.operation-result.success {
  background: #28a745;
}

.operation-result.error {
  background: #dc3545;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    min-width: auto;
  }

  .action-buttons {
    justify-content: center;
  }

  .preview-info {
    grid-template-columns: 1fr;
  }
}
</style>
