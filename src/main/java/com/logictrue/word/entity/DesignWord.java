package com.logictrue.word.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.logictrue.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 检验记录表设计对象 design_word
 */
@ApiModel(description = "检验记录表设计")
@TableName("design_word")
public class DesignWord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车辆ID
     */
    @ApiModelProperty(value = "车辆ID")
    @TableField("car_id")
    private String carId;

    /**
     * 表格标题
     */
    @ApiModelProperty(value = "表格标题")
    @TableField("title")
    private String title;

    /**
     * 表格配置信息(JSON格式)
     */
    @ApiModelProperty(value = "表格配置信息")
    @TableField("table_config")
    private String tableConfig;

    /**
     * 表格数据(JSON格式)
     */
    @ApiModelProperty(value = "表格数据")
    @TableField("table_data")
    private String tableData;

    /**
     * 状态(0:禁用 1:启用)
     */
    @ApiModelProperty(value = "状态")
    @TableField("status")
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCarId() {
        return carId;
    }

    public void setCarId(String carId) {
        this.carId = carId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTableConfig() {
        return tableConfig;
    }

    public void setTableConfig(String tableConfig) {
        this.tableConfig = tableConfig;
    }

    public String getTableData() {
        return tableData;
    }

    public void setTableData(String tableData) {
        this.tableData = tableData;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("carId", getCarId())
                .append("title", getTitle())
                .append("tableConfig", getTableConfig())
                .append("tableData", getTableData())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
