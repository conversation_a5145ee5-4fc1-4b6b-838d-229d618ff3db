package com.logictrue.word.service;

import com.logictrue.word.dto.SimpleWordExportRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.wp.usermodel.HeaderFooterType;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.util.Units;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageMar;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageSz;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STPageOrientation;
import org.springframework.stereotype.Service;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 简单Word导出服务
 */
@Slf4j
@Service
public class SimpleWordExportService {

    /**
     * 导出简单Word文档
     */
    public byte[] exportSimpleWord(SimpleWordExportRequest request) throws IOException {
        log.info("开始导出简单Word文档，标题: {}", request.getTitle());

        // 创建Word文档
        XWPFDocument document = new XWPFDocument();

        try {
            // 设置页面属性
            setPageSettings(document, request.getPageSettings());

            // 添加页眉页脚
            addHeaderFooter(document, request.getPageSettings());

            // 解析HTML内容并添加到文档
            parseAndAddContent(document, request.getContent());

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);

            log.info("简单Word文档导出成功，大小: {} bytes", outputStream.size());
            return outputStream.toByteArray();

        } finally {
            document.close();
        }
    }

    /**
     * 设置页面属性
     */
    private void setPageSettings(XWPFDocument document, SimpleWordExportRequest.PageSettings settings) {
        if (settings == null) {
            return;
        }

        // 获取文档的节属性
        CTSectPr sectPr = document.getDocument().getBody().addNewSectPr();
        CTPageSz pageSize = sectPr.addNewPgSz();
        CTPageMar pageMargin = sectPr.addNewPgMar();

        // 设置纸张大小
        setPaperSize(pageSize, settings.getPaperSize(), settings.getOrientation());

        // 设置页边距（转换毫米到twips，1毫米 = 56.7 twips）
        int marginTopTwips = (int) (settings.getMarginTop() * 56.7);
        int marginBottomTwips = (int) (settings.getMarginBottom() * 56.7);
        int marginLeftTwips = (int) (settings.getMarginLeft() * 56.7);
        int marginRightTwips = (int) (settings.getMarginRight() * 56.7);

        pageMargin.setTop(BigInteger.valueOf(marginTopTwips));
        pageMargin.setBottom(BigInteger.valueOf(marginBottomTwips));
        pageMargin.setLeft(BigInteger.valueOf(marginLeftTwips));
        pageMargin.setRight(BigInteger.valueOf(marginRightTwips));

        log.debug("页面设置完成: 纸张={}, 方向={}, 边距={}mm",
                 settings.getPaperSize(), settings.getOrientation(),
                 String.format("上%d 下%d 左%d 右%d",
                     settings.getMarginTop(), settings.getMarginBottom(),
                     settings.getMarginLeft(), settings.getMarginRight()));
    }

    /**
     * 设置纸张大小
     */
    private void setPaperSize(CTPageSz pageSize, String paperSizeType, String orientation) {
        boolean isLandscape = "landscape".equals(orientation);

        switch (paperSizeType) {
            case "A4":
                if (isLandscape) {
                    pageSize.setW(BigInteger.valueOf(16838)); // A4横向宽度
                    pageSize.setH(BigInteger.valueOf(11906)); // A4横向高度
                } else {
                    pageSize.setW(BigInteger.valueOf(11906)); // A4纵向宽度
                    pageSize.setH(BigInteger.valueOf(16838)); // A4纵向高度
                }
                break;
            case "A3":
                if (isLandscape) {
                    pageSize.setW(BigInteger.valueOf(23811)); // A3横向宽度
                    pageSize.setH(BigInteger.valueOf(16838)); // A3横向高度
                } else {
                    pageSize.setW(BigInteger.valueOf(16838)); // A3纵向宽度
                    pageSize.setH(BigInteger.valueOf(23811)); // A3纵向高度
                }
                break;
            case "Letter":
                if (isLandscape) {
                    pageSize.setW(BigInteger.valueOf(15840)); // Letter横向宽度
                    pageSize.setH(BigInteger.valueOf(12240)); // Letter横向高度
                } else {
                    pageSize.setW(BigInteger.valueOf(12240)); // Letter纵向宽度
                    pageSize.setH(BigInteger.valueOf(15840)); // Letter纵向高度
                }
                break;
            default:
                // 默认A4纵向
                pageSize.setW(BigInteger.valueOf(11906));
                pageSize.setH(BigInteger.valueOf(16838));
        }

        if (isLandscape) {
            pageSize.setOrient(STPageOrientation.LANDSCAPE);
        }
    }

    /**
     * 添加页眉页脚
     */
    private void addHeaderFooter(XWPFDocument document, SimpleWordExportRequest.PageSettings settings) {
        if (settings == null) {
            return;
        }

        try {
            // 添加页眉
            if (settings.getShowHeader() && settings.getHeaderText() != null && !settings.getHeaderText().trim().isEmpty()) {
                XWPFHeader header = document.createHeader(HeaderFooterType.DEFAULT);
                XWPFParagraph headerParagraph = header.createParagraph();
                headerParagraph.setAlignment(ParagraphAlignment.CENTER);

                XWPFRun headerRun = headerParagraph.createRun();
                headerRun.setText(settings.getHeaderText());
                headerRun.setFontFamily(settings.getHeaderFooterFont());
                headerRun.setFontSize(settings.getHeaderFooterFontSize());

                log.debug("添加页眉: {}", settings.getHeaderText());
            }

            // 添加页脚
            if (settings.getShowFooter() || settings.getShowPageNumber()) {
                XWPFFooter footer = document.createFooter(HeaderFooterType.DEFAULT);
                XWPFParagraph footerParagraph = footer.createParagraph();
                footerParagraph.setAlignment(ParagraphAlignment.CENTER);

                XWPFRun footerRun = footerParagraph.createRun();

                StringBuilder footerText = new StringBuilder();
                if (settings.getShowFooter() && settings.getFooterText() != null && !settings.getFooterText().trim().isEmpty()) {
                    footerText.append(settings.getFooterText());
                }

                if (settings.getShowPageNumber()) {
                    if (footerText.length() > 0) {
                        footerText.append(" ");
                    }
                    footerText.append("第 ");
                }

                footerRun.setText(footerText.toString());
                footerRun.setFontFamily(settings.getHeaderFooterFont());
                footerRun.setFontSize(settings.getHeaderFooterFontSize());

                // 添加页码
                if (settings.getShowPageNumber()) {
                    footerParagraph.getCTP().addNewFldSimple().setInstr("PAGE");
                    footerRun = footerParagraph.createRun();
                    footerRun.setText(" 页");
                    footerRun.setFontFamily(settings.getHeaderFooterFont());
                    footerRun.setFontSize(settings.getHeaderFooterFontSize());
                }

                log.debug("添加页脚: {}", footerText.toString());
            }

        } catch (Exception e) {
            log.warn("添加页眉页脚时出现错误", e);
        }
    }

    /**
     * 解析HTML内容并添加到文档
     */
    private void parseAndAddContent(XWPFDocument document, String htmlContent) {
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            // 添加默认内容
            XWPFParagraph paragraph = document.createParagraph();
            XWPFRun run = paragraph.createRun();
            run.setText("文档内容为空");
            return;
        }

        try {
            // 使用Jsoup解析HTML
            Document doc = Jsoup.parse(htmlContent);
            Elements elements = doc.body().children();

            for (Element element : elements) {
                processElement(document, element);
            }

        } catch (Exception e) {
            log.error("解析HTML内容时出现错误", e);
            // 如果解析失败，添加纯文本内容
            XWPFParagraph paragraph = document.createParagraph();
            XWPFRun run = paragraph.createRun();
            run.setText(Jsoup.parse(htmlContent).text());
        }
    }

    /**
     * 处理HTML元素
     */
    private void processElement(XWPFDocument document, Element element) {
        String tagName = element.tagName().toLowerCase();
        XWPFParagraph paragraph = document.createParagraph();
        switch (tagName) {
            case "p":
            case "div":
                processParagraph(document, element);
                break;
            case "img":
                processImage(document, element);
                break;
            case "br":
                // 添加换行
                paragraph.createRun();
                break;
            default:
                // 处理其他元素，提取文本内容
                if (!element.text().trim().isEmpty()) {
                    XWPFRun run = paragraph.createRun();
                    run.setText(element.text());
                    applyTextStyle(run, element);
                }
                break;
        }
    }

    /**
     * 处理段落元素
     */
    private void processParagraph(XWPFDocument document, Element element) {
        XWPFParagraph paragraph = document.createParagraph();

        // 设置段落对齐方式
        String textAlign = element.attr("style");
        if (textAlign.contains("text-align: center")) {
            paragraph.setAlignment(ParagraphAlignment.CENTER);
        } else if (textAlign.contains("text-align: right")) {
            paragraph.setAlignment(ParagraphAlignment.RIGHT);
        } else {
            paragraph.setAlignment(ParagraphAlignment.LEFT);
        }

        // 处理段落内容
        if (element.children().isEmpty()) {
            // 纯文本段落
            XWPFRun run = paragraph.createRun();
            run.setText(element.text());
            applyTextStyle(run, element);
        } else {
            // 包含子元素的段落
            processInlineElements(paragraph, element);
        }
    }

    /**
     * 处理行内元素
     */
    private void processInlineElements(XWPFParagraph paragraph, Element element) {
        for (org.jsoup.nodes.Node node : element.childNodes()) {
            if (node instanceof org.jsoup.nodes.TextNode) {
                // 文本节点
                String text = ((org.jsoup.nodes.TextNode) node).text();
                if (!text.trim().isEmpty()) {
                    XWPFRun run = paragraph.createRun();
                    run.setText(text);
                    applyTextStyle(run, element);
                }
            } else if (node instanceof Element) {
                // 元素节点
                Element childElement = (Element) node;
                XWPFRun run = paragraph.createRun();
                run.setText(childElement.text());
                applyTextStyle(run, childElement);
            }
        }
    }

    /**
     * 应用文本样式
     */
    private void applyTextStyle(XWPFRun run, Element element) {
        String style = element.attr("style");
        String tagName = element.tagName().toLowerCase();

        // 处理字体样式
        if (style.contains("font-weight: bold") || "b".equals(tagName) || "strong".equals(tagName)) {
            run.setBold(true);
        }

        if (style.contains("font-style: italic") || "i".equals(tagName) || "em".equals(tagName)) {
            run.setItalic(true);
        }

        if (style.contains("text-decoration: underline") || "u".equals(tagName)) {
            run.setUnderline(UnderlinePatterns.SINGLE);
        }

        if (style.contains("text-decoration: line-through") || "s".equals(tagName) || "strike".equals(tagName)) {
            run.setStrikeThrough(true);
        }

        // 处理字体大小
        Pattern fontSizePattern = Pattern.compile("font-size:\\s*(\\d+)px");
        Matcher fontSizeMatcher = fontSizePattern.matcher(style);
        if (fontSizeMatcher.find()) {
            int fontSize = Integer.parseInt(fontSizeMatcher.group(1));
            run.setFontSize(fontSize);
        }

        // 处理字体族
        Pattern fontFamilyPattern = Pattern.compile("font-family:\\s*([^;]+)");
        Matcher fontFamilyMatcher = fontFamilyPattern.matcher(style);
        if (fontFamilyMatcher.find()) {
            String fontFamily = fontFamilyMatcher.group(1).trim().replaceAll("[\"']", "");
            run.setFontFamily(fontFamily);
        }

        // 处理字体颜色
        Pattern colorPattern = Pattern.compile("color:\\s*(#[0-9a-fA-F]{6}|rgb\\([^)]+\\))");
        Matcher colorMatcher = colorPattern.matcher(style);
        if (colorMatcher.find()) {
            String color = colorMatcher.group(1);
            if (color.startsWith("#")) {
                run.setColor(color.substring(1));
            }
        }
    }

    /**
     * 处理图片元素
     */
    private void processImage(XWPFDocument document, Element element) {
        try {
            String src = element.attr("src");
            if (src.startsWith("data:image/")) {
                // Base64图片
                String[] parts = src.split(",");
                if (parts.length == 2) {
                    byte[] imageBytes = Base64.getDecoder().decode(parts[1]);

                    XWPFParagraph paragraph = document.createParagraph();
                    XWPFRun run = paragraph.createRun();

                    // 确定图片格式
                    int format = XWPFDocument.PICTURE_TYPE_PNG;
                    if (parts[0].contains("jpeg") || parts[0].contains("jpg")) {
                        format = XWPFDocument.PICTURE_TYPE_JPEG;
                    }

                    // 插入图片，设置默认大小
                    run.addPicture(new java.io.ByteArrayInputStream(imageBytes), format, "image",
                                  Units.toEMU(200), Units.toEMU(150));

                    log.debug("插入Base64图片，大小: {} bytes", imageBytes.length);
                }
            }
        } catch (Exception e) {
            log.warn("处理图片时出现错误", e);
        }
    }
}
