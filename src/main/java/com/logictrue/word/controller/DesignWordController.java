package com.logictrue.word.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logictrue.common.core.web.controller.BaseController;
import com.logictrue.common.core.web.domain.AjaxResult;
import com.logictrue.common.core.web.page.TableDataInfo;
import com.logictrue.word.entity.DesignWord;
import com.logictrue.word.service.IDesignWordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 检验记录表设计Controller
 */
@RestController
@RequestMapping("/designWord")
@Api(value = "检验记录表设计", tags = "检验记录表设计")
public class DesignWordController extends BaseController {

    @Autowired
    private IDesignWordService designWordService;

    /**
     * 查询检验记录表设计列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询检验记录表设计列表")
    public TableDataInfo list(DesignWord designWord) {
        startPage();
        List<DesignWord> list = designWordService.selectDesignWordList(designWord);
        return getDataTable(list);
    }

    /**
     * 分页查询检验记录表设计列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询检验记录表设计列表")
    public AjaxResult page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("车辆ID") @RequestParam(required = false) String carId,
            @ApiParam("标题") @RequestParam(required = false) String title,
            @ApiParam("状态") @RequestParam(required = false) Integer status) {
        
        try {
            Page<DesignWord> page = new Page<>(pageNum, pageSize);
            DesignWord designWord = new DesignWord();
            designWord.setCarId(carId);
            designWord.setTitle(title);
            designWord.setStatus(status);
            
            IPage<DesignWord> result = designWordService.selectDesignWordPage(page, designWord);
            return AjaxResult.success(result);
            
        } catch (Exception e) {
            logger.error("分页查询检验记录表设计失败", e);
            return AjaxResult.error("分页查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取检验记录表设计详细信息
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取检验记录表设计详细信息")
    public AjaxResult getInfo(@ApiParam("主键ID") @PathVariable("id") Long id) {
        try {
            DesignWord designWord = designWordService.selectDesignWordById(id);
            return AjaxResult.success(designWord);
        } catch (Exception e) {
            logger.error("获取检验记录表设计详细信息失败", e);
            return AjaxResult.error("获取详细信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据车辆ID获取检验记录表设计
     */
    @GetMapping("/byCarId/{carId}")
    @ApiOperation(value = "根据车辆ID获取检验记录表设计")
    public AjaxResult getByCarId(@ApiParam("车辆ID") @PathVariable("carId") String carId) {
        try {
            DesignWord designWord = designWordService.selectDesignWordByCarId(carId);
            return AjaxResult.success(designWord);
        } catch (Exception e) {
            logger.error("根据车辆ID获取检验记录表设计失败", e);
            return AjaxResult.error("获取设计信息失败：" + e.getMessage());
        }
    }

    /**
     * 新增检验记录表设计
     */
    @PostMapping
    @ApiOperation(value = "新增检验记录表设计")
    public AjaxResult add(@RequestBody DesignWord designWord) {
        try {
            // 设置默认值
            if (designWord.getStatus() == null) {
                designWord.setStatus(1);
            }
            if (designWord.getTitle() == null || designWord.getTitle().trim().isEmpty()) {
                designWord.setTitle("检验记录表");
            }
            
            int result = designWordService.insertDesignWord(designWord);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("新增检验记录表设计失败", e);
            return AjaxResult.error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改检验记录表设计
     */
    @PutMapping
    @ApiOperation(value = "修改检验记录表设计")
    public AjaxResult edit(@RequestBody DesignWord designWord) {
        try {
            int result = designWordService.updateDesignWord(designWord);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("修改检验记录表设计失败", e);
            return AjaxResult.error("修改失败：" + e.getMessage());
        }
    }

    /**
     * 保存或更新检验记录表设计
     */
    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "保存或更新检验记录表设计")
    public AjaxResult saveOrUpdate(@RequestBody DesignWord designWord) {
        try {
            // 设置默认值
            if (designWord.getStatus() == null) {
                designWord.setStatus(1);
            }
            if (designWord.getTitle() == null || designWord.getTitle().trim().isEmpty()) {
                designWord.setTitle("检验记录表");
            }
            
            int result = designWordService.saveOrUpdateDesignWord(designWord);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("保存或更新检验记录表设计失败", e);
            return AjaxResult.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 删除检验记录表设计
     */
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除检验记录表设计")
    public AjaxResult remove(@ApiParam("主键ID数组") @PathVariable Long[] ids) {
        try {
            int result = designWordService.deleteDesignWordByIds(ids);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("删除检验记录表设计失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }
}
