package com.logictrue.word.dto;

import lombok.Data;
import java.util.Map;

/**
 * 简单Word导出请求DTO
 */
@Data
public class SimpleWordExportRequest {
    
    /**
     * 文档标题
     */
    private String title;
    
    /**
     * 文档内容（HTML格式）
     */
    private String content;
    
    /**
     * 页面设置
     */
    private PageSettings pageSettings;
    
    /**
     * 页面设置类
     */
    @Data
    public static class PageSettings {
        /**
         * 纸张大小
         */
        private String paperSize = "A4";
        
        /**
         * 页面方向
         */
        private String orientation = "portrait";
        
        /**
         * 上边距（毫米）
         */
        private Integer marginTop = 25;
        
        /**
         * 下边距（毫米）
         */
        private Integer marginBottom = 25;
        
        /**
         * 左边距（毫米）
         */
        private Integer marginLeft = 30;
        
        /**
         * 右边距（毫米）
         */
        private Integer marginRight = 30;
        
        /**
         * 是否显示页眉
         */
        private Boolean showHeader = false;
        
        /**
         * 页眉内容
         */
        private String headerText = "";
        
        /**
         * 是否显示页脚
         */
        private Boolean showFooter = false;
        
        /**
         * 页脚内容
         */
        private String footerText = "";
        
        /**
         * 是否显示页码
         */
        private Boolean showPageNumber = true;
        
        /**
         * 页眉页脚字体
         */
        private String headerFooterFont = "Microsoft YaHei";
        
        /**
         * 页眉页脚字体大小
         */
        private Integer headerFooterFontSize = 12;
        
        /**
         * 页眉页脚字体颜色
         */
        private String headerFooterColor = "#666666";
    }
}
