import request from '@/utils/request'

// 查询检验记录表设计列表
export function listDesignWord(query) {
  return request({
    url: '/word/designWord/list',
    method: 'get',
    params: query
  })
}

// 分页查询检验记录表设计列表
export function pageDesignWord(params) {
  return request({
    url: '/word/designWord/page',
    method: 'get',
    params
  })
}

// 查询检验记录表设计详细
export function getDesignWord(id) {
  return request({
    url: '/word/designWord/' + id,
    method: 'get'
  })
}

// 根据车辆ID查询检验记录表设计
export function getDesignWordByCarId(carId) {
  return request({
    url: '/word/designWord/byCarId/' + carId,
    method: 'get'
  })
}

// 新增检验记录表设计
export function addDesignWord(data) {
  return request({
    url: '/word/designWord',
    method: 'post',
    data: data
  })
}

// 修改检验记录表设计
export function updateDesignWord(data) {
  return request({
    url: '/word/designWord',
    method: 'put',
    data: data
  })
}

// 保存或更新检验记录表设计
export function saveOrUpdateDesignWord(data) {
  return request({
    url: '/word/designWord/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 删除检验记录表设计
export function delDesignWord(ids) {
  return request({
    url: '/word/designWord/' + ids,
    method: 'delete'
  })
}
